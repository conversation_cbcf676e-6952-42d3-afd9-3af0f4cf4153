"use client";
import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, Card, Typography, message, Spin, Alert } from 'antd';
import { CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { createWorkspace, checkWorkspaceIdAvailability, getUserOwnedWorkspacesCount } from '@/app/actions/workspace';
import Header from '@/app/components/Header';

const { Title, Text } = Typography;

interface FormValues {
  workspaceName: string;
  workspaceId: string;
}

const OnboardingPage = () => {
  const t = useTranslations('Onboarding');
  const router = useRouter();
  const { data: session, status } = useSession();
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingId, setIsCheckingId] = useState(false);
  const [idAvailable, setIdAvailable] = useState<boolean | null>(null);
  const [, setWorkspaceCount] = useState(0);
  const [isLoadingWorkspaces, setIsLoadingWorkspaces] = useState(false);
  const [hasReachedLimit, setHasReachedLimit] = useState(false);
  const isCheckingWorkspaceLimit = useRef(false);

  // 检查用户是否已登录
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 检查用户创建的工作空间数量（只统计作为 owner 的工作空间）
  useEffect(() => {
    const checkWorkspaceLimit = async () => {
      // 防止重复执行
      if (isCheckingWorkspaceLimit.current) {
        return;
      }

      // 只有在认证完成且有用户ID时才执行检查
      if (status === 'authenticated' && session?.user?.id) {
        isCheckingWorkspaceLimit.current = true;
        setIsLoadingWorkspaces(true);
        try {
          const result = await getUserOwnedWorkspacesCount();
          if (result.status === 'success') {
            const count = result.data;
            setWorkspaceCount(count);
            const maxWorkspaces = 1; // 公测期间限制为1个工作空间
            setHasReachedLimit(count >= maxWorkspaces);
          } else {
            console.error('Failed to get workspace count:', result.message);
          }
        } catch (error) {
          console.error('Error checking workspace limit:', error);
        } finally {
          setIsLoadingWorkspaces(false);
          isCheckingWorkspaceLimit.current = false;
        }
      } else if (status === 'unauthenticated') {
        // 如果用户未认证，重置检查状态
        isCheckingWorkspaceLimit.current = false;
        setIsLoadingWorkspaces(false);
      }
    };

    checkWorkspaceLimit();
  }, [status, session?.user?.id]);

  // 检查 workspace ID 可用性
  const checkIdAvailability = async (id: string) => {
    if (!id || id.length < 5) {
      setIdAvailable(null);
      return;
    }

    setIsCheckingId(true);
    try {
      const result = await checkWorkspaceIdAvailability(id);
      setIdAvailable(result.available);
    } catch (error) {
      setIdAvailable(false);
    } finally {
      setIsCheckingId(false);
    }
  };


  // 处理表单提交
  const onFinish = async (values: FormValues) => {
    if (hasReachedLimit) {
      message.error('您已创建了 1 个工作空间，公测期间暂不支持创建更多');
      return;
    }

    if (!idAvailable) {
      message.error(t('idUnavailableError'));
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await createWorkspace({
        id: values.workspaceId,
        name: values.workspaceName,
      });

      if (result.status === 'success') {
        message.success(t('createSuccess'));
        // 跳转到新创建的 workspace 聊天页面
        router.push(`/${result.data?.id}/chat`);
      } else {
        message.error(result.message || t('createFailed'));
      }
    } catch (error) {
      message.error(t('createError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 生成建议的 workspace ID
  const generateSuggestedId = () => {
    const workspaceName = form.getFieldValue('workspaceName');
    if (workspaceName) {
      const suggested = workspaceName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .slice(0, 10);
      form.setFieldsValue({ workspaceId: suggested });
      // 触发表单验证
      form.validateFields(['workspaceId']);
      checkIdAvailability(suggested);
    }
  };



  // 只有在认证状态为loading时才显示loading，避免不必要的重复渲染
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  // 如果正在检查工作空间限制且还没有认证完成，显示loading
  if (status === 'authenticated' && isLoadingWorkspaces) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <Title level={2}>{t('welcome')}</Title>
            <Text type="secondary">
              {t('subtitle')}
            </Text>
          </div>

          <Card className="shadow-lg">
            {/* 工作空间创建限制提示 */}
            {hasReachedLimit && (
              <Alert
                message={<>您已创建了 1 个工作空间（公测期间限制），<Link href="/workspaces">返回列表</Link></>}
                type="warning"
                showIcon
                style={{ marginBottom: '12px' }}
              />
            )}

            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              autoComplete="off"
              disabled={hasReachedLimit}
            >
              <Form.Item
                label={t('workspaceName')}
                name="workspaceName"
                validateTrigger="onBlur"
                rules={[
                  { required: true, message: t('workspaceNameRequired') },
                  { min: 2, message: t('workspaceNameMinLength') },
                  { max: 50, message: t('workspaceNameMaxLength') }
                ]}
              >
                <Input
                  placeholder={t('workspaceNamePlaceholder')}
                  size="large"
                  onBlur={generateSuggestedId}
                />
              </Form.Item>

              <Form.Item
                label={t('workspaceId')}
                name="workspaceId"
                validateTrigger="onBlur"
                rules={[
                  { required: true, message: t('workspaceIdRequired') || '请输入工作区 ID' },
                  { min: 5, message: t('workspaceIdMinLength') || 'ID 至少需要 5 个字符' },
                  { max: 20, message: t('workspaceIdMaxLength') || 'ID 不能超过 20 个字符' },
                  { pattern: /^[a-z0-9-]+$/, message: t('workspaceIdPattern') || '只能包含小写字母、数字和连字符' }
                ]}
              >
                <Input
                  placeholder={t('workspaceIdPlaceholder')}
                  size="large"
                  onBlur={()=>{checkIdAvailability(form.getFieldValue('workspaceId'))}}
                  suffix={
                    isCheckingId ? (
                      <LoadingOutlined />
                    ) : idAvailable === true ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : idAvailable === false ? (
                      <Text type="danger" className="text-xs">{t('workspaceIdUnavailable')}</Text>
                    ) : null
                  }
                />
              </Form.Item>

              {/* 帮助文本 */}
              <div className="mb-4 -mt-2">
                <Text type="secondary" className="text-xs">
                  {t('workspaceIdHelp')}{form.getFieldValue('workspaceId') || ''}
                </Text>
              </div>

              <Form.Item className="mb-0">
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={isSubmitting}
                  disabled={!idAvailable || hasReachedLimit}
                >
                  {hasReachedLimit ? '已达到创建限制（公测期间）' : t('createWorkspace')}
                </Button>
              </Form.Item>
            </Form>
          </Card>

          <div className="text-center">
            <Text type="secondary" className="text-sm">
              {t('teamNotice')}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;